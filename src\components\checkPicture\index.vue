<template>
  <div class="checkPicture">
    <!-- <div class="checkPicture-title"></div> -->
    <div class="checkPicture-content">
      <div class="content-top">
        <div class="top-left">
          <div class="icon"></div>
          <div class="title">法人单位：{{ projectInfo.legalUnitName }}</div>
        </div>
        <div class="top-right">
          <div
            class="btn"
            :class="index == 0 ? 'btn-s' : 'btn-n'"
            @click="click(0)"
          >
            工程布置图
          </div>
          <div
            class="btn"
            :class="index == 1 ? 'btn-s' : 'btn-n'"
            @click="click(1)"
          >
            大事记
          </div>
        </div>
      </div>
      <div class="content-pic" v-show="index == 0">
        <el-carousel
          ref="picCarousel"
          :interval="4294967295"
          arrow="always"
          :autoplay="false"
          :show-indicators="false"
          :pause-on-hover="true"
          :motion-blur="false"
          :trigger="'click'"
        >
          <el-carousel-item
            v-for="item in fileList"
            :key="item.uuid || item.name"
          >
            <el-image style="height: 100%" :src="item.url" fit="cover" />
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="content-dsj" v-show="index == 1">
        <div class="dsj-container">
          <div class="dsj-nav">
            <button
              class="dsj-nav-btn dsj-nav-prev"
              @click="prevDsj"
              :disabled="currentDsjIndex <= 0"
            >
              ‹
            </button>
            <button
              class="dsj-nav-btn dsj-nav-next"
              @click="nextDsj"
              :disabled="currentDsjIndex >= dsjList.length - 1"
            >
              ›
            </button>
          </div>
          <div
            class="dsj-item"
            v-if="dsjList.length > 0 && dsjList[currentDsjIndex]"
          >
            <el-image
              style="height: 100%"
              :src="
                dsjList[currentDsjIndex].picList.length > 0
                  ? dsjList[currentDsjIndex].picList[0].url
                  : ''
              "
              fit="cover"
            />
            <div class="dsj-title">
              {{ typeDic[dsjList[currentDsjIndex].type] }}
            </div>
            <div class="dsj-content">
              <div class="dsj-c-title">{{ dsjList[currentDsjIndex].name }}</div>
              <div class="dsj-c-name">
                标段名称：{{ dsjList[currentDsjIndex].sectionName }}
              </div>
              <div class="dsj-c-time">
                {{
                  moment(dsjList[currentDsjIndex].eventDate).format(
                    "YYYY-MM-DD"
                  )
                }}
              </div>
              <div class="dsj-c-desc">
                {{ dsjList[currentDsjIndex].description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
import moment from "moment";
const store = useStore();
const props = defineProps({
  type: Number,
});
let index = ref(0);
const fileList = ref([]);
const projectInfo = ref({ legalUnitName: "" });
const dsjList = ref([]);
// NODE关键节点、MEETING重要会议、SURVEY领导视察
const typeDic = ref({
  NODE: "关键节点",
  MEETING: "重要会议",
  SURVEY: "领导视察",
});

const click = (i) => {
  index.value = i;
  console.log(i);
};

onMounted(() => {
  getLayoutDrawings({
    projectId: store.state.projectId,
    sectionId: store.state.sectionId,
  });
  getMajorEvents({
    projectId: store.state.projectId,
    sectionId: store.state.sectionId,
  });
  getProjectDetails(store.state.projectId);
});

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    getLayoutDrawings({ projectId, sectionId });
    getMajorEvents({ projectId, sectionId });
    if (sectionId == null) {
      getProjectDetails(projectId);
    }
  }
);

const getProjectDetails = async (projectId) => {
  if (projectId == null || projectId == "") return;
  const res = await api.getProjectDetails(projectId);
  projectInfo.value = res.result;
};

const getLayoutDrawings = async (params) => {
  const res = await api.getLayoutDrawings(params);
  fileList.value = res.result;
  console.log("zc判断---", fileList.value);

  if (fileList.value && fileList.value.length > 0) {
    // fileList.value.forEach((item) => {
    //   getFileList(item, item.uuid);
    // });
    const item = fileList.value[0];
    getFileList(item, item.uuid);

    if (fileList.value.length > 1) {
      const item1 = fileList.value[1];
      getFileList(item1, item1.uuid);
    }

    // getFileList(res.result.map((item) => item.uuid), item);
  }
};

const getMajorEvents = async (params) => {
  const res = await api.getMajorEvents(params);
  dsjList.value = res;
  console.log("判断---", dsjList.value, dsjList.value.length == 0);
  if (dsjList.value.length > 0) {
    const item = dsjList.value[0];
    if (item.picList) {
      item.picList.forEach((listItem) => {
        getFileList(listItem, listItem.fileId);
      });
    }
  }
  if (dsjList.value.length > 1) {
    const item = dsjList.value[1];
    if (item.picList) {
      item.picList.forEach((listItem) => {
        getFileList(listItem, listItem.fileId);
      });
    }
  }
  // if (dsjList.value) {
  //   dsjList.value.forEach(item => {
  //     if (item.picList) {
  //       item.picList.forEach
  //         (listItem => {
  //           getFileList(listItem, listItem.uuid);
  //         });
  //     }
  //   });
  // }
};

async function getFileList(item, uuid) {
  const imgUrl = await _getImgUrlData({
    fileUUIDList: [uuid],
    fileType: 0,
  });
  if (Array.isArray(imgUrl) && imgUrl.length > 0) {
    imgUrl.map((listItem) => {
      // fileList.value.push({
      //   name: listItem.fileName,
      //   url: listItem.downloadUrls[0],
      // });
      item.url = listItem.downloadUrls[0];
      item.name = listItem.fileName;
    });
  }
}

// 根据 uuid 获取图片
async function _getImgUrlData(params) {
  const imgData = await api.postFileaddressLongLineDownloadURLs(params);
  return imgData;
}
</script>

<style lang="less" scoped>
.checkPicture {
  width: 58.5rem;
  height: 44.6875rem;

  .checkPicture-title {
    background: url("../../assets/img/checkPicture/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .checkPicture-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: calc(100% - 2.25rem);
    width: calc(100% - 2rem);
    position: absolute;

    .content-top {
      height: 2.5rem;
      margin: 1rem;
      background: linear-gradient(
        to right,
        rgba(71, 152, 247, 0.2),
        rgba(71, 152, 247, 0)
      );
      display: flex;
      justify-content: space-between;
      align-items: center;

      .top-left {
        display: flex;
        align-items: center;
        margin-left: 1rem;

        .icon {
          background: url("../../assets/img/checkPicture/icon.png") center
            center no-repeat;
          background-size: 100% 100%;
          height: 1.5rem;
          width: 1.5rem;
        }

        .title {
          margin-left: 1rem;
          font-size: 1rem;
          font-weight: 700;
        }
      }

      .top-right {
        display: flex;
        align-items: center;

        .btn {
          width: 5.875rem;
          height: 2rem;
          font-size: 0.875rem;
          font-weight: 700;
          margin-left: 1rem;
          line-height: 2rem;
          cursor: pointer;
          //   &:hover {
          //     color: #3DFFFF;
          //   }
        }

        .btn-s {
          background: url("../../assets/img/checkPicture/btn-s.png") center
            center no-repeat;
          background-size: 100% 100%;
          color: #3dffff;
        }

        .btn-n {
          background: url("../../assets/img/checkPicture/btn-n.png") center
            center no-repeat;
          background-size: 100% 100%;
          color: #fff;
        }
      }
    }

    .content-pic,
    .content-dsj {
      margin: 1rem;
      background: url("../../assets/img/checkPicture/pic-border.png") center
        center no-repeat;
      background-size: 100% 100%;
      margin-top: 1rem;
      height: calc(100% - 2.5rem - 1rem - 2.5rem);
      border: rgba(44, 163, 255, 0.5) 1px solid;

      :deep(.el-carousel) {
        margin: 0.5rem;
        height: calc(100% - 1rem);

        .el-carousel__container {
          height: 100%;
        }

        .el-carousel__item {
          height: 100%;
        }

        .el-carousel__indicators,
        .el-carousel__indicators--horizontal {
          opacity: 0;
        }

        .el-image {
          width: 100%;
          height: 100%;
        }
      }
    }

    .content-dsj {
      .dsj-title {
        position: absolute;
        left: 3rem;
        top: 2rem;
        font-size: 1.5rem;
        font-weight: 700;
      }

      .dsj-content {
        position: absolute;
        left: 2.5rem;
        bottom: 2rem;
        text-align: left;

        .dsj-c-title {
          font-size: 1.2rem;
          font-weight: 500;
        }

        .dsj-c-name {
          margin-top: 1rem;
        }

        .dsj-c-time {
          margin-top: 0.5rem;
        }

        .dsj-c-desc {
          margin-top: 0.5rem;
        }
      }
    }
  }
}
</style>
