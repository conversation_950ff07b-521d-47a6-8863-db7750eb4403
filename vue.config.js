const { defineConfig } = require("@vue/cli-service");
module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: "./",

  devServer: {
    open: true, //是否自动弹出浏览器页面
    // host: "localhost",  //也可以换成localhost      host: "localhost",
    port: 8081,
    // https: true,
    // hotOnly: false,
    //以上的ip和端口是我们本机的;下面为需要跨域的
    proxy: {
      //配置跨域
      "/api": {
        // target: 'http://*************:8182/gateway',   //你的后端端口前部分 ？之前的部分
        // target: "http://nxsl.lubansoft.net:8182",
        target: "http://************:8182",
        changeOrigin: true, // 虚拟的站点需要更管origin     //允许跨域
        pathRewrite: {
          "^/api": "", //请求的时候使用这个api就可以
        },
      },
      // 代理登录回调
      "/auth-callback": {
        target: "http://**************:8182",
        changeOrigin: true,
        pathRewrite: {
          "^/auth-callback": "",
        },
      },
    },
  },
});
