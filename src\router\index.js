import { createRouter, createWebHashHistory } from 'vue-router'
import ScheduleManagement from '../views/ScheduleManagement.vue'
import ProjectLevel from '../views/ProjectLevel.vue'

const routes = [
  {
    path: '/',
    name: 'scheduleManagement',
    component: ScheduleManagement
  },
  {
    path: '/safetyManagement',
    name: 'safetyManagement',
    component: () => import('../views/SafetyManagement.vue')
  },
  {
    path: '/qualityControl',
    name: 'qualityControl',
    component: () => import('../views/QualityControl.vue')
  },
  {
    path: '/videoSurveillance',
    name: 'videoSurveillance',
    component: () => import('../views/VideoSurveillance.vue')
  },
  {
    path: '/projectLevel',
    name: 'projectLevel',
    component: ProjectLevel
  },
  {
    path: '/VideoListView',
    name: 'VideoListView',
    component: () => import('../views/VideoListView.vue')
  },
  {
    path: '/VideoList',
    name: 'VideoList',
    component: () => import('../views/VideoList.vue')
  },
  {
    path: '/ProgressManagementView',
    name: 'ProgressManagementView',
    component: () => import('../views/ProgressManagementView.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
